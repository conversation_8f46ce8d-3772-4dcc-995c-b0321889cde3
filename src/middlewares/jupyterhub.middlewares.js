import { createProxyMiddleware } from 'http-proxy-middleware';
import config from '../config/database.config.js';
import logger from '../config/logger.config.js';

export const jupyterProxy = createProxyMiddleware({
  target: config.jupyterhub.url,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  followRedirects: true,

  pathRewrite: (path, req) => {
    if (!req.user || !req.user.jupiterUserName) {
      // This error should ideally be caught by an error handler
      throw new Error('User not found on request object for proxying.');
    }
    const username = req.user.jupiterUserName;

    // --- THIS IS THE CRITICAL FIX ---
    // The 'path' variable is everything AFTER '/api/jupyter' (e.g., '/api/contents/My-Project').
    // We need to prepend the user's specific path prefix to it.
    const newPath = `/user/${username}${path}`;

    logger.info(`Rewriting path for '${username}': '${path}' --> '${newPath}'`);

    return newPath;
  },

  onProxyReq: (proxyReq, req, res) => {
    console.log(
      `--> PROXYING request for user: ${req.user.jupiterUserName} to ${config.jupyterhub.url}`
    );
    proxyReq.setHeader('Authorization', `token ${config.jupyterhub.apiToken}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    // logger.info(
    //   `<-- RECEIVED ${proxyRes.statusCode} from ${config.jupyterhub.url}`
    // );
  },
  onError: (err, req, res) => {
    logger.error('PROXY ERROR:', err);
    if (!res.headersSent) {
      res.status(504).send('Proxy Gateway Timeout.');
    }
  }
});
