import { createProxyMiddleware } from 'http-proxy-middleware';
import config from '../config/database.config.js';
import logger from '../config/logger.config.js';
import jupyterService from '../services/jupyterhub.service.js';
// Simple in-memory token cache to avoid minting a new token on every request
const tokenCache = new Map(); // username -> token
// Clear cache on startup to ensure fresh tokens with correct scopes
tokenCache.clear();

// Helper function to clear token cache (useful for debugging)
export const clearTokenCache = () => {
  tokenCache.clear();
  logger.info('Token cache cleared');
};

// Ensure a per-user Jupyter token exists and attach to req for proxying
export const ensureJupyterToken = async (req, res, next) => {
  try {
    if (!req.user || !req.user.jupiterUserName) {
      return res
        .status(400)
        .json({ message: 'Missing user for Jupyter proxy' });
    }
    const username = req.user.jupiterUserName;

    // Jupyter Server accepts either cookie (via Hub auth) or token header.
    // We’ll mint a user token and cache it in memory.
    let token = tokenCache.get(username);
    if (!token) {
      const tokenResp = await jupyterService.getUserToken(username);
      token = tokenResp.token || tokenResp;
      tokenCache.set(username, token);
    }
    req.user.jupyterToken = token;

    return next();
  } catch (err) {
    logger.error(
      'Failed to ensure Jupyter token:',
      err.response?.data || err.message
    );
    return res
      .status(502)
      .json({ message: 'Failed to prepare Jupyter proxy auth' });
  }
};

export const jupyterProxy = createProxyMiddleware({
  target: config.jupyterhub.url,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  followRedirects: true,

  pathRewrite: (path, req) => {
    if (!req.user || !req.user.jupiterUserName) {
      // This error should ideally be caught by an error handler
      throw new Error('User not found on request object for proxying.');
    }
    const username = req.user.jupiterUserName;

    // --- THIS IS THE CRITICAL FIX ---
    // The 'path' variable is everything AFTER '/api/jupyter' (e.g., '/api/contents/My-Project').
    // We need to prepend the user's specific path prefix to it.
    const newPath = `/user/${username}${path}`;

    logger.info(`Rewriting path for '${username}': '${path}' --> '${newPath}'`);

    return newPath;
  },

  onProxyReq: (proxyReq, req) => {
    const username = req.user?.jupiterUserName;
    logger.info(
      `--> PROXYING request for user: ${username} to ${config.jupyterhub.url}`
    );
    // Get user token from request and use it for authentication
    const userToken = req.user?.jupyterToken;
    if (userToken) {
      proxyReq.setHeader('Authorization', `token ${userToken}`);
    } else {
      // Fallback to admin token
      proxyReq.setHeader(
        'Authorization',
        `token ${config.jupyterhub.apiToken}`
      );
    }
  },
  onProxyRes: () => {
    // no-op for now
  },
  onProxyReqWs: (proxyReq, req, _socket, _options, _head) => {
    // Get user token from request and use it for WebSocket authentication
    const userToken = req.user?.jupyterToken;
    if (userToken) {
      proxyReq.setHeader('Authorization', `token ${userToken}`);
    } else {
      // Fallback to admin token
      proxyReq.setHeader(
        'Authorization',
        `token ${config.jupyterhub.apiToken}`
      );
    }
  },
  onError: (err, _req, res) => {
    logger.error('PROXY ERROR:', err);
    if (!res.headersSent) {
      res.status(504).send('Proxy Gateway Timeout.');
    }
  }
});
