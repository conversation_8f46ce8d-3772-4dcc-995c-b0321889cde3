# JupyterHub configuration for BITS DataScience Platform
import os

# Basic configuration
c.JupyterHub.ip = '0.0.0.0'
c.JupyterHub.port = 8001
c.JupyterHub.hub_ip = '0.0.0.0'

# Admin configuration
c.JupyterHub.admin_access = True
c.JupyterHub.allow_named_servers = True

# Authentication - use DummyAuthenticator for development
# In production, you might want to use a different authenticator
c.JupyterHub.authenticator_class = 'jupyterhub.auth.DummyAuthenticator'

# Spawner configuration
c.JupyterHub.spawner_class = 'jupyterhub.spawner.LocalProcessSpawner'
c.Spawner.default_url = '/lab'
c.Spawner.cmd = ['jupyter-labhub']

# Database configuration
c.JupyterHub.db_url = 'sqlite:///jupyterhub.sqlite'

# Service configuration for backend integration
c.JupyterHub.services = [
    {
        'name': 'backend-service',
        'api_token': os.environ.get('JUPYTERHUB_ADMIN_TOKEN', '607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6'),
        'admin': True,
        'url': 'http://localhost:5000',  # Your backend URL
    }
]

# CORS configuration for API access
c.JupyterHub.tornado_settings = {
    'headers': {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    }
}

# API configuration
c.JupyterHub.api_tokens = {
    os.environ.get('JUPYTERHUB_ADMIN_TOKEN', '607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6'): 'backend-service'
}

# Security settings
c.JupyterHub.cookie_secret_file = 'jupyterhub_cookie_secret'
c.JupyterHub.proxy_auth_token = os.environ.get('CONFIGPROXY_AUTH_TOKEN', 'your-configproxy-token')

# Logging
c.JupyterHub.log_level = 'DEBUG'
c.Application.log_level = 'DEBUG'

# User management
c.Authenticator.auto_login = False
c.Authenticator.create_system_users = False

# Spawner settings for better integration
c.Spawner.notebook_dir = '~/notebooks'
c.Spawner.args = ['--allow-root']

# Network settings
c.JupyterHub.bind_url = 'http://0.0.0.0:8001'

# Enable user server access via API
c.JupyterHub.load_roles = [
    {
        'name': 'server-access',
        'scopes': [
            'access:servers',
            'read:users:activity',
            'read:servers',
            'delete:servers',
            'servers',
            'read:users:name',
            'read:users:groups',
            'read:shares',
            'read:users',
            'users:shares',
            'users:activity',
            'read:users:shares',
            'tokens',
            'read:tokens',
            'self',
        ],
        'services': ['backend-service'],
    }
]
