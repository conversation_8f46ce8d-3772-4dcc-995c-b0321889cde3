{"name": "bits-datascience-backend", "version": "1.0.0", "description": "Backend API for BITS-DataScience Projects Platform with LTI 1.3 and S3 integration", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "npm run clean && npm run copy-static", "clean": "rm -rf dist && mkdir -p dist", "copy-static": "cp -r src/ dist/", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "test:unit": "jest --testPathPattern=tests/unit", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:seed": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo:all", "db:reset": "npm run db:migrate:undo && npm run db:migrate && npm run db:seed", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop", "db:backup": "node scripts/backup-db.js", "health": "node scripts/health-check.js", "setup": "npm install && npm run db:create && npm run db:migrate && npm run db:seed", "docker:build": "docker build -t bits-datascience-backend .", "docker:run": "docker run -p 5000:5000 bits-datascience-backend", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:stop": "docker-compose down", "logs": "tail -f logs/app.log", "logs:error": "tail -f logs/error.log", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs", "security:audit": "npm audit", "security:fix": "npm audit fix", "docs:generate": "node scripts/generate-docs.js", "docs:serve": "node scripts/serve-docs.js", "swagger:validate": "swagger-jsdoc -d swaggerDef.js src/routes/*.js", "precommit": "npm run lint && npm run format:check && npm run test", "prepare": "husky install"}, "keywords": ["bits-pilani", "education", "lti", "jup<PERSON><PERSON>", "data-science", "learning-management", "nodejs", "express", "postgresql", "sequelize", "lti-1.3", "aws-s3", "brightspace"], "author": {"name": "BITS Pilani", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bits-pilani/datascience-platform-backend"}, "bugs": {"url": "https://github.com/bits-pilani/datascience-platform-backend/issues"}, "homepage": "https://github.com/bits-pilani/datascience-platform-backend#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@aws-sdk/client-codebuild": "^3.866.0", "@aws-sdk/s3-request-presigner": "^3.859.0", "archiver": "^6.0.1", "aws-sdk": "^2.1498.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.1", "compression": "^1.7.4", "connect-redis": "^7.1.0", "connect-session-sequelize": "^7.1.7", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-slow-down": "^1.6.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "http-proxy-middleware": "^3.0.5", "http-status": "^2.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "jwk-to-pem": "^2.0.5", "lodash": "^4.17.21", "mammoth": "^1.6.0", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "node-jose": "^2.2.0", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.6.11", "sequelize": "^6.35.1", "sequelize-cli": "^6.6.2", "sharp": "^0.32.6", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5", "xss": "^1.0.14"}, "devDependencies": {"@babel/preset-env": "^7.28.3", "@types/node": "^20.10.4", "artillery": "^2.0.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-security": "^1.7.1", "factory-girl": "^5.0.4", "faker": "^5.5.3", "husky": "^8.0.3", "jest": "^29.7.0", "jest-extended": "^4.0.2", "jest-junit": "^16.0.0", "lint-staged": "^15.2.0", "newman": "^6.0.0", "nock": "^13.5.0", "nodemon": "^3.0.2", "nyc": "^15.1.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "sinon": "^17.0.1", "supertest": "^6.3.4", "swagger-parser": "^10.0.3", "wait-on": "^7.2.0"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "nodemonConfig": {"watch": ["src/"], "ext": "js,json", "ignore": ["node_modules/", "tests/", "logs/"], "exec": "node src/server.js"}}